<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\JobController;

// Homepage
Route::get('/', [JobController::class, 'index'])->name('jobs.index');

// Jobs listing and search
Route::get('/jobs', [JobController::class, 'jobs'])->name('jobs.jobs');
Route::get('/jobs/search', [JobController::class, 'search'])->name('jobs.search');

// Individual job pages
Route::get('/jobs/{id}', [JobController::class, 'show'])->name('jobs.show');
Route::get('/jobs/{id}/apply', [JobController::class, 'apply'])->name('jobs.apply');
Route::post('/jobs/{id}/apply', [JobController::class, 'submitApplication'])->name('jobs.submit-application');

// Static pages
Route::get('/about', [JobController::class, 'about'])->name('jobs.about');
Route::get('/contact', [JobController::class, 'contact'])->name('jobs.contact');
Route::post('/contact', [JobController::class, 'submitContact'])->name('jobs.submit-contact');
