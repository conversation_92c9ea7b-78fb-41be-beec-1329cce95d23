@extends('layouts.app')

@section('title', 'Apply for ' . $job->title . ' at ' . $job->company . ' - Global Jobs Portal')
@section('description', 'Apply for international job opportunity with visa sponsorship and work permit assistance.')

@section('content')
<!-- Application Header -->
<section class="hero-section py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Breadcrumb -->
        <nav class="mb-6">
            <ol class="flex items-center space-x-2 text-sm text-gray-300">
                <li><a href="{{ route('jobs.index') }}" class="hover:text-orange-500">Home</a></li>
                <li><i class="fas fa-chevron-right text-orange-500 mx-2"></i></li>
                <li><a href="{{ route('jobs.jobs') }}" class="hover:text-orange-500">Jobs</a></li>
                <li><i class="fas fa-chevron-right text-orange-500 mx-2"></i></li>
                <li><a href="{{ route('jobs.show', $job->id) }}" class="hover:text-orange-500">{{ $job->title }}</a></li>
                <li><i class="fas fa-chevron-right text-orange-500 mx-2"></i></li>
                <li class="text-orange-500">Apply</li>
            </ol>
        </nav>
        
        <div class="text-center">
            <h1 class="text-3xl md:text-4xl font-black text-white tracking-wider uppercase mb-4">
                APPLY FOR <span class="text-orange-500">POSITION</span>
            </h1>
            <div class="bg-white border-l-8 border-orange-500 p-6 inline-block shadow-lg">
                <h2 class="text-xl font-black text-slate-900 tracking-wide uppercase">{{ $job->title }}</h2>
                <p class="text-orange-500 font-bold">{{ $job->company }}</p>
                <p class="text-gray-600 flex items-center justify-center mt-2">
                    <i class="fas fa-map-marker-alt text-orange-500 mr-2"></i>
                    {{ $job->location }}, {{ $job->country }}
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Application Form -->
<section class="py-12 bg-gray-50">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        @if(session('success'))
            <div class="bg-green-100 border-l-8 border-green-500 p-6 mb-8 shadow-lg">
                <div class="flex items-center">
                    <i class="fas fa-check-circle text-green-500 text-2xl mr-4"></i>
                    <div>
                        <h3 class="text-lg font-bold text-green-800">Application Submitted Successfully!</h3>
                        <p class="text-green-700">{{ session('success') }}</p>
                    </div>
                </div>
            </div>
        @endif

        @if($errors->any())
            <div class="bg-red-100 border-l-8 border-red-500 p-6 mb-8 shadow-lg">
                <div class="flex items-start">
                    <i class="fas fa-exclamation-triangle text-red-500 text-2xl mr-4 mt-1"></i>
                    <div>
                        <h3 class="text-lg font-bold text-red-800 mb-2">Please correct the following errors:</h3>
                        <ul class="list-disc list-inside text-red-700">
                            @foreach($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                </div>
            </div>
        @endif

        <form action="{{ route('jobs.submitApplication', $job->id) }}" method="POST" enctype="multipart/form-data" class="bg-white border-l-8 border-orange-500 p-8 shadow-lg">
            @csrf
            
            <!-- Personal Information -->
            <div class="mb-12">
                <h3 class="text-2xl font-black text-slate-900 tracking-wider uppercase mb-6 border-l-4 border-orange-500 pl-4">
                    PERSONAL INFORMATION
                </h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-bold text-slate-900 tracking-wide uppercase mb-2">
                            FIRST NAME *
                        </label>
                        <input type="text" name="first_name" value="{{ old('first_name') }}" 
                               class="form-input @error('first_name') border-red-500 @enderror" required>
                        @error('first_name')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div>
                        <label class="block text-sm font-bold text-slate-900 tracking-wide uppercase mb-2">
                            LAST NAME *
                        </label>
                        <input type="text" name="last_name" value="{{ old('last_name') }}" 
                               class="form-input @error('last_name') border-red-500 @enderror" required>
                        @error('last_name')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div>
                        <label class="block text-sm font-bold text-slate-900 tracking-wide uppercase mb-2">
                            EMAIL ADDRESS *
                        </label>
                        <input type="email" name="email" value="{{ old('email') }}" 
                               class="form-input @error('email') border-red-500 @enderror" required>
                        @error('email')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div>
                        <label class="block text-sm font-bold text-slate-900 tracking-wide uppercase mb-2">
                            PHONE NUMBER *
                        </label>
                        <input type="tel" name="phone" value="{{ old('phone') }}" 
                               class="form-input @error('phone') border-red-500 @enderror" required>
                        @error('phone')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div>
                        <label class="block text-sm font-bold text-slate-900 tracking-wide uppercase mb-2">
                            CURRENT COUNTRY *
                        </label>
                        <input type="text" name="current_country" value="{{ old('current_country') }}" 
                               class="form-input @error('current_country') border-red-500 @enderror" required>
                        @error('current_country')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div>
                        <label class="block text-sm font-bold text-slate-900 tracking-wide uppercase mb-2">
                            CURRENT CITY *
                        </label>
                        <input type="text" name="current_city" value="{{ old('current_city') }}" 
                               class="form-input @error('current_city') border-red-500 @enderror" required>
                        @error('current_city')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>
            
            <!-- Professional Information -->
            <div class="mb-12">
                <h3 class="text-2xl font-black text-slate-900 tracking-wider uppercase mb-6 border-l-4 border-orange-500 pl-4">
                    PROFESSIONAL INFORMATION
                </h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <label class="block text-sm font-bold text-slate-900 tracking-wide uppercase mb-2">
                            YEARS OF EXPERIENCE *
                        </label>
                        <select name="experience_years" class="form-select @error('experience_years') border-red-500 @enderror" required>
                            <option value="">Select Experience</option>
                            <option value="0-1" {{ old('experience_years') == '0-1' ? 'selected' : '' }}>0-1 years</option>
                            <option value="2-3" {{ old('experience_years') == '2-3' ? 'selected' : '' }}>2-3 years</option>
                            <option value="4-5" {{ old('experience_years') == '4-5' ? 'selected' : '' }}>4-5 years</option>
                            <option value="6-10" {{ old('experience_years') == '6-10' ? 'selected' : '' }}>6-10 years</option>
                            <option value="10+" {{ old('experience_years') == '10+' ? 'selected' : '' }}>10+ years</option>
                        </select>
                        @error('experience_years')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div>
                        <label class="block text-sm font-bold text-slate-900 tracking-wide uppercase mb-2">
                            CURRENT POSITION
                        </label>
                        <input type="text" name="current_position" value="{{ old('current_position') }}" 
                               class="form-input @error('current_position') border-red-500 @enderror">
                        @error('current_position')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div>
                        <label class="block text-sm font-bold text-slate-900 tracking-wide uppercase mb-2">
                            CURRENT COMPANY
                        </label>
                        <input type="text" name="current_company" value="{{ old('current_company') }}" 
                               class="form-input @error('current_company') border-red-500 @enderror">
                        @error('current_company')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div>
                        <label class="block text-sm font-bold text-slate-900 tracking-wide uppercase mb-2">
                            EXPECTED SALARY ({{ $job->currency ?? 'USD' }})
                        </label>
                        <input type="text" name="expected_salary" value="{{ old('expected_salary') }}" 
                               placeholder="e.g. 50000-60000" class="form-input @error('expected_salary') border-red-500 @enderror">
                        @error('expected_salary')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
                
                <div class="mb-6">
                    <label class="block text-sm font-bold text-slate-900 tracking-wide uppercase mb-2">
                        SKILLS & EXPERTISE
                    </label>
                    <textarea name="skills" rows="3" placeholder="List your relevant skills and expertise..." 
                              class="form-textarea @error('skills') border-red-500 @enderror">{{ old('skills') }}</textarea>
                    @error('skills')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
            </div>
            
            <!-- Documents -->
            <div class="mb-12">
                <h3 class="text-2xl font-black text-slate-900 tracking-wider uppercase mb-6 border-l-4 border-orange-500 pl-4">
                    DOCUMENTS
                </h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-bold text-slate-900 tracking-wide uppercase mb-2">
                            RESUME/CV * (PDF, DOC, DOCX - Max 5MB)
                        </label>
                        <input type="file" name="resume" accept=".pdf,.doc,.docx" 
                               class="form-input @error('resume') border-red-500 @enderror" required>
                        @error('resume')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div>
                        <label class="block text-sm font-bold text-slate-900 tracking-wide uppercase mb-2">
                            COVER LETTER (PDF, DOC, DOCX - Max 5MB)
                        </label>
                        <input type="file" name="cover_letter" accept=".pdf,.doc,.docx" 
                               class="form-input @error('cover_letter') border-red-500 @enderror">
                        @error('cover_letter')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>
            
            <!-- Additional Information -->
            <div class="mb-12">
                <h3 class="text-2xl font-black text-slate-900 tracking-wider uppercase mb-6 border-l-4 border-orange-500 pl-4">
                    ADDITIONAL INFORMATION
                </h3>
                
                <div class="mb-6">
                    <label class="block text-sm font-bold text-slate-900 tracking-wide uppercase mb-2">
                        WHY ARE YOU INTERESTED IN THIS POSITION?
                    </label>
                    <textarea name="motivation" rows="4" placeholder="Tell us why you're interested in this role and what makes you a great fit..." 
                              class="form-textarea @error('motivation') border-red-500 @enderror">{{ old('motivation') }}</textarea>
                    @error('motivation')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div class="mb-6">
                    <label class="block text-sm font-bold text-slate-900 tracking-wide uppercase mb-2">
                        AVAILABILITY
                    </label>
                    <select name="availability" class="form-select @error('availability') border-red-500 @enderror">
                        <option value="">Select Availability</option>
                        <option value="Immediately" {{ old('availability') == 'Immediately' ? 'selected' : '' }}>Immediately</option>
                        <option value="2 weeks" {{ old('availability') == '2 weeks' ? 'selected' : '' }}>2 weeks notice</option>
                        <option value="1 month" {{ old('availability') == '1 month' ? 'selected' : '' }}>1 month notice</option>
                        <option value="2 months" {{ old('availability') == '2 months' ? 'selected' : '' }}>2 months notice</option>
                        <option value="3+ months" {{ old('availability') == '3+ months' ? 'selected' : '' }}>3+ months notice</option>
                    </select>
                    @error('availability')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <!-- International Requirements -->
                @if($job->visa_sponsorship || $job->work_permit_assistance)
                <div class="bg-gray-50 border-l-4 border-orange-500 p-6 mb-6">
                    <h4 class="text-lg font-bold text-slate-900 tracking-wide uppercase mb-4">
                        INTERNATIONAL REQUIREMENTS
                    </h4>
                    
                    <div class="space-y-4">
                        @if($job->visa_sponsorship)
                        <div>
                            <label class="flex items-center">
                                <input type="checkbox" name="requires_visa_sponsorship" value="1" 
                                       {{ old('requires_visa_sponsorship') ? 'checked' : '' }}
                                       class="mr-3 h-4 w-4 text-orange-500 focus:ring-orange-500 border-gray-300">
                                <span class="text-sm font-semibold text-slate-900">
                                    I require visa sponsorship for this position
                                </span>
                            </label>
                        </div>
                        @endif
                        
                        @if($job->work_permit_assistance)
                        <div>
                            <label class="flex items-center">
                                <input type="checkbox" name="requires_work_permit" value="1" 
                                       {{ old('requires_work_permit') ? 'checked' : '' }}
                                       class="mr-3 h-4 w-4 text-orange-500 focus:ring-orange-500 border-gray-300">
                                <span class="text-sm font-semibold text-slate-900">
                                    I require work permit assistance for this position
                                </span>
                            </label>
                        </div>
                        @endif
                        
                        @if($job->relocation_assistance)
                        <div>
                            <label class="flex items-center">
                                <input type="checkbox" name="requires_relocation" value="1" 
                                       {{ old('requires_relocation') ? 'checked' : '' }}
                                       class="mr-3 h-4 w-4 text-orange-500 focus:ring-orange-500 border-gray-300">
                                <span class="text-sm font-semibold text-slate-900">
                                    I require relocation assistance for this position
                                </span>
                            </label>
                        </div>
                        @endif
                    </div>
                </div>
                @endif
            </div>
            
            <!-- Terms and Conditions -->
            <div class="mb-8">
                <div class="bg-gray-50 border-l-4 border-orange-500 p-6">
                    <label class="flex items-start">
                        <input type="checkbox" name="terms_accepted" value="1" required
                               class="mr-3 h-4 w-4 text-orange-500 focus:ring-orange-500 border-gray-300 mt-1">
                        <span class="text-sm text-slate-900">
                            I agree to the <a href="#" class="text-orange-500 hover:underline">Terms and Conditions</a> 
                            and <a href="#" class="text-orange-500 hover:underline">Privacy Policy</a>. 
                            I consent to the processing of my personal data for recruitment purposes and understand 
                            that my information may be shared with the hiring company.
                        </span>
                    </label>
                </div>
            </div>
            
            <!-- Submit Button -->
            <div class="text-center">
                <button type="submit" class="btn-primary text-lg px-12 py-4">
                    <i class="fas fa-paper-plane mr-2"></i>SUBMIT APPLICATION
                </button>
            </div>
        </form>
    </div>
</section>

<!-- Job Summary Sidebar -->
<section class="py-12 bg-slate-900 border-t-8 border-orange-500">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h2 class="text-3xl font-black text-white tracking-wider uppercase mb-4">
                APPLYING FOR
            </h2>
            <div class="bg-white border-l-8 border-orange-500 p-6 inline-block shadow-lg max-w-2xl">
                <h3 class="text-2xl font-black text-slate-900 tracking-wide uppercase mb-2">{{ $job->title }}</h3>
                <p class="text-orange-500 font-bold text-lg mb-2">{{ $job->company }}</p>
                <p class="text-gray-600 flex items-center justify-center mb-4">
                    <i class="fas fa-map-marker-alt text-orange-500 mr-2"></i>
                    {{ $job->location }}, {{ $job->country }}
                </p>
                
                @if($job->salary_range)
                <p class="text-lg font-bold text-slate-900 mb-4">{{ $job->formatted_salary }}</p>
                @endif
                
                <div class="flex justify-center gap-2 mb-4">
                    <span class="badge-slate">{{ $job->job_type }}</span>
                    <span class="badge-outline">{{ $job->category }}</span>
                </div>
                
                <a href="{{ route('jobs.show', $job->id) }}" class="btn-secondary">
                    <i class="fas fa-eye mr-2"></i>VIEW JOB DETAILS
                </a>
            </div>
        </div>
    </div>
</section>
@endsection
