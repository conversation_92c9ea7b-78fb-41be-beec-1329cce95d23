@extends('layouts.app')

@section('title', $job->title . ' at ' . $job->company . ' - Global Jobs Portal')
@section('description', Str::limit($job->description, 160))

@section('content')
<!-- Job Header -->
<section class="hero-section py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col lg:flex-row items-start justify-between">
            <div class="flex-1">
                <!-- Breadcrumb -->
                <nav class="mb-6">
                    <ol class="flex items-center space-x-2 text-sm text-gray-300">
                        <li><a href="{{ route('jobs.index') }}" class="hover:text-orange-500">Home</a></li>
                        <li><i class="fas fa-chevron-right text-orange-500 mx-2"></i></li>
                        <li><a href="{{ route('jobs.jobs') }}" class="hover:text-orange-500">Jobs</a></li>
                        <li><i class="fas fa-chevron-right text-orange-500 mx-2"></i></li>
                        <li class="text-orange-500">{{ $job->title }}</li>
                    </ol>
                </nav>
                
                <!-- Job Title & Company -->
                <div class="flex items-start mb-6">
                    <div class="w-20 h-20 bg-white flex items-center justify-center mr-6 flex-shrink-0">
                        <i class="fas fa-building text-slate-900 text-2xl"></i>
                    </div>
                    <div>
                        <h1 class="text-3xl md:text-4xl font-black text-white tracking-wider uppercase mb-2">
                            {{ $job->title }}
                        </h1>
                        <p class="text-xl text-orange-500 font-bold mb-2">{{ $job->company }}</p>
                        <p class="text-gray-300 flex items-center">
                            <i class="fas fa-map-marker-alt text-orange-500 mr-2"></i>
                            {{ $job->location }}, {{ $job->country }}
                        </p>
                    </div>
                </div>
                
                <!-- Job Meta -->
                <div class="flex flex-wrap gap-3 mb-6">
                    @if($job->is_featured)
                        <span class="badge-orange">FEATURED</span>
                    @endif
                    <span class="bg-white text-slate-900 px-3 py-1 text-sm font-bold tracking-wide uppercase">
                        {{ $job->job_type }}
                    </span>
                    <span class="bg-white text-slate-900 px-3 py-1 text-sm font-bold tracking-wide uppercase">
                        {{ $job->category }}
                    </span>
                    <span class="bg-white text-slate-900 px-3 py-1 text-sm font-bold tracking-wide uppercase">
                        {{ $job->experience_level }}
                    </span>
                </div>
            </div>
            
            <!-- Apply Button -->
            <div class="w-full lg:w-auto mt-6 lg:mt-0">
                <a href="{{ route('jobs.apply', $job->id) }}" class="btn-primary w-full lg:w-auto text-center block">
                    <i class="fas fa-paper-plane mr-2"></i>APPLY NOW
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Job Content -->
<section class="py-12 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-12">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <!-- Job Description -->
                <div class="bg-white border-l-8 border-orange-500 p-8 shadow-lg mb-8">
                    <h2 class="text-2xl font-black text-slate-900 tracking-wider uppercase mb-6 border-l-4 border-orange-500 pl-4">
                        JOB DESCRIPTION
                    </h2>
                    <div class="prose prose-lg max-w-none text-gray-700">
                        {!! nl2br(e($job->description)) !!}
                    </div>
                </div>
                
                <!-- Requirements -->
                <div class="bg-white border-l-8 border-orange-500 p-8 shadow-lg mb-8">
                    <h2 class="text-2xl font-black text-slate-900 tracking-wider uppercase mb-6 border-l-4 border-orange-500 pl-4">
                        REQUIREMENTS
                    </h2>
                    <div class="prose prose-lg max-w-none text-gray-700">
                        {!! nl2br(e($job->requirements)) !!}
                    </div>
                </div>
                
                <!-- Responsibilities -->
                @if($job->responsibilities)
                <div class="bg-white border-l-8 border-orange-500 p-8 shadow-lg mb-8">
                    <h2 class="text-2xl font-black text-slate-900 tracking-wider uppercase mb-6 border-l-4 border-orange-500 pl-4">
                        RESPONSIBILITIES
                    </h2>
                    <div class="prose prose-lg max-w-none text-gray-700">
                        {!! nl2br(e($job->responsibilities)) !!}
                    </div>
                </div>
                @endif
                
                <!-- Benefits -->
                @if($job->benefits)
                <div class="bg-white border-l-8 border-orange-500 p-8 shadow-lg mb-8">
                    <h2 class="text-2xl font-black text-slate-900 tracking-wider uppercase mb-6 border-l-4 border-orange-500 pl-4">
                        BENEFITS & PERKS
                    </h2>
                    <div class="prose prose-lg max-w-none text-gray-700">
                        {!! nl2br(e($job->benefits)) !!}
                    </div>
                </div>
                @endif
                
                <!-- Skills & Languages -->
                @if($job->skills || $job->languages)
                <div class="bg-white border-l-8 border-orange-500 p-8 shadow-lg mb-8">
                    <h2 class="text-2xl font-black text-slate-900 tracking-wider uppercase mb-6 border-l-4 border-orange-500 pl-4">
                        SKILLS & LANGUAGES
                    </h2>
                    
                    @if($job->skills)
                    <div class="mb-6">
                        <h3 class="text-lg font-bold text-slate-900 tracking-wide uppercase mb-3">Required Skills</h3>
                        <div class="flex flex-wrap gap-2">
                            @foreach($job->skills as $skill)
                                <span class="badge-slate">{{ $skill }}</span>
                            @endforeach
                        </div>
                    </div>
                    @endif
                    
                    @if($job->languages)
                    <div>
                        <h3 class="text-lg font-bold text-slate-900 tracking-wide uppercase mb-3">Languages</h3>
                        <div class="flex flex-wrap gap-2">
                            @foreach($job->languages as $language)
                                <span class="badge-outline">{{ $language }}</span>
                            @endforeach
                        </div>
                    </div>
                    @endif
                </div>
                @endif
            </div>
            
            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Job Summary -->
                <div class="bg-white border-l-8 border-orange-500 p-6 shadow-lg mb-8">
                    <h3 class="text-xl font-black text-slate-900 tracking-wider uppercase mb-6">
                        JOB SUMMARY
                    </h3>
                    
                    <div class="space-y-4">
                        @if($job->salary_range)
                        <div class="flex justify-between items-center py-2 border-b border-gray-200">
                            <span class="text-gray-600 font-semibold">Salary:</span>
                            <span class="text-slate-900 font-bold">{{ $job->formatted_salary }}</span>
                        </div>
                        @endif
                        
                        <div class="flex justify-between items-center py-2 border-b border-gray-200">
                            <span class="text-gray-600 font-semibold">Job Type:</span>
                            <span class="text-slate-900 font-bold">{{ $job->job_type }}</span>
                        </div>
                        
                        <div class="flex justify-between items-center py-2 border-b border-gray-200">
                            <span class="text-gray-600 font-semibold">Experience:</span>
                            <span class="text-slate-900 font-bold">{{ $job->experience_level }}</span>
                        </div>
                        
                        <div class="flex justify-between items-center py-2 border-b border-gray-200">
                            <span class="text-gray-600 font-semibold">Category:</span>
                            <span class="text-slate-900 font-bold">{{ $job->category }}</span>
                        </div>
                        
                        @if($job->application_deadline)
                        <div class="flex justify-between items-center py-2 border-b border-gray-200">
                            <span class="text-gray-600 font-semibold">Deadline:</span>
                            <span class="text-slate-900 font-bold">{{ $job->application_deadline->format('M d, Y') }}</span>
                        </div>
                        @endif
                        
                        <div class="flex justify-between items-center py-2">
                            <span class="text-gray-600 font-semibold">Views:</span>
                            <span class="text-slate-900 font-bold">{{ number_format($job->views) }}</span>
                        </div>
                    </div>
                </div>
                
                <!-- International Benefits -->
                <div class="bg-white border-l-8 border-orange-500 p-6 shadow-lg mb-8">
                    <h3 class="text-xl font-black text-slate-900 tracking-wider uppercase mb-6">
                        INTERNATIONAL SUPPORT
                    </h3>
                    
                    <div class="space-y-4">
                        <div class="flex items-center">
                            <i class="fas fa-passport text-2xl {{ $job->visa_sponsorship ? 'text-green-500' : 'text-gray-300' }} mr-3"></i>
                            <div>
                                <p class="font-bold text-slate-900">Visa Sponsorship</p>
                                <p class="text-sm text-gray-600">{{ $job->visa_sponsorship ? 'Available' : 'Not Available' }}</p>
                            </div>
                        </div>
                        
                        <div class="flex items-center">
                            <i class="fas fa-file-contract text-2xl {{ $job->work_permit_assistance ? 'text-green-500' : 'text-gray-300' }} mr-3"></i>
                            <div>
                                <p class="font-bold text-slate-900">Work Permit</p>
                                <p class="text-sm text-gray-600">{{ $job->work_permit_assistance ? 'Assistance Provided' : 'Not Provided' }}</p>
                            </div>
                        </div>
                        
                        <div class="flex items-center">
                            <i class="fas fa-plane text-2xl {{ $job->relocation_assistance ? 'text-green-500' : 'text-gray-300' }} mr-3"></i>
                            <div>
                                <p class="font-bold text-slate-900">Relocation Support</p>
                                <p class="text-sm text-gray-600">{{ $job->relocation_assistance ? 'Available' : 'Not Available' }}</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Contact Information -->
                <div class="bg-white border-l-8 border-orange-500 p-6 shadow-lg mb-8">
                    <h3 class="text-xl font-black text-slate-900 tracking-wider uppercase mb-6">
                        CONTACT INFO
                    </h3>
                    
                    <div class="space-y-3">
                        <div class="flex items-center">
                            <i class="fas fa-envelope text-orange-500 mr-3"></i>
                            <a href="mailto:{{ $job->contact_email }}" class="text-slate-900 hover:text-orange-500">
                                {{ $job->contact_email }}
                            </a>
                        </div>
                        
                        @if($job->contact_phone)
                        <div class="flex items-center">
                            <i class="fas fa-phone text-orange-500 mr-3"></i>
                            <a href="tel:{{ $job->contact_phone }}" class="text-slate-900 hover:text-orange-500">
                                {{ $job->contact_phone }}
                            </a>
                        </div>
                        @endif
                    </div>
                </div>
                
                <!-- Apply Button -->
                <div class="bg-slate-900 border-l-8 border-orange-500 p-6 shadow-lg text-center">
                    <h3 class="text-xl font-black text-white tracking-wider uppercase mb-4">
                        READY TO APPLY?
                    </h3>
                    <p class="text-gray-300 mb-6">
                        Take the next step in your international career journey.
                    </p>
                    <a href="{{ route('jobs.apply', $job->id) }}" class="btn-primary w-full">
                        <i class="fas fa-paper-plane mr-2"></i>APPLY NOW
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Jobs -->
@if($relatedJobs->count() > 0)
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-black text-slate-900 tracking-wider uppercase mb-4 border-l-8 border-orange-500 pl-6 inline-block">
                RELATED OPPORTUNITIES
            </h2>
            <p class="text-gray-600 text-lg">Other jobs you might be interested in</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            @foreach($relatedJobs as $relatedJob)
                <div class="job-card group">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-slate-900 flex items-center justify-center">
                            <i class="fas fa-building text-orange-500"></i>
                        </div>
                        @if($relatedJob->is_featured)
                            <span class="badge-orange">FEATURED</span>
                        @endif
                    </div>
                    
                    <h3 class="text-lg font-black text-slate-900 tracking-wide uppercase mb-2 group-hover:text-orange-500 transition-colors">
                        {{ Str::limit($relatedJob->title, 40) }}
                    </h3>
                    <p class="text-gray-600 font-semibold mb-2">{{ $relatedJob->company }}</p>
                    <p class="text-gray-500 mb-4 flex items-center">
                        <i class="fas fa-map-marker-alt text-orange-500 mr-2"></i>
                        {{ $relatedJob->location }}, {{ $relatedJob->country }}
                    </p>
                    
                    <div class="flex flex-wrap gap-2 mb-4">
                        <span class="badge-slate text-xs">{{ $relatedJob->job_type }}</span>
                        <span class="badge-outline text-xs">{{ $relatedJob->category }}</span>
                    </div>
                    
                    <a href="{{ route('jobs.show', $relatedJob->id) }}" class="btn-primary w-full text-center text-sm">
                        VIEW DETAILS
                    </a>
                </div>
            @endforeach
        </div>
    </div>
</section>
@endif
@endsection
