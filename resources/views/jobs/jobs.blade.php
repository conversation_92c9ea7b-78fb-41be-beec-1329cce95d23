@extends('layouts.app')

@section('title', 'Browse International Jobs - Global Jobs Portal')
@section('description', 'Browse thousands of international job opportunities with visa sponsorship and work permit assistance.')

@section('content')
<!-- Page Header -->
<section class="hero-section py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl md:text-5xl font-black text-white tracking-wider uppercase mb-4">
                INTERNATIONAL <span class="text-orange-500">OPPORTUNITIES</span>
            </h1>
            <p class="text-xl text-gray-300 max-w-2xl mx-auto">
                Discover your next career opportunity with visa sponsorship and relocation support
            </p>
        </div>
    </div>
</section>

<!-- Filters Section -->
<section class="py-8 bg-gray-100 border-b-4 border-orange-500">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <form method="GET" action="{{ route('jobs.jobs') }}" class="bg-white border-l-8 border-orange-500 p-6 shadow-lg">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
                <!-- Search -->
                <div class="lg:col-span-2">
                    <label class="block text-sm font-bold text-slate-900 tracking-wide uppercase mb-2">
                        SEARCH JOBS
                    </label>
                    <input type="text" name="search" placeholder="Job title, company, keywords..." 
                           class="form-input" value="{{ request('search') }}">
                </div>
                
                <!-- Country Filter -->
                <div>
                    <label class="block text-sm font-bold text-slate-900 tracking-wide uppercase mb-2">
                        COUNTRY
                    </label>
                    <select name="country" class="form-select">
                        <option value="">All Countries</option>
                        @foreach($countries as $country)
                            <option value="{{ $country }}" {{ request('country') == $country ? 'selected' : '' }}>
                                {{ $country }}
                            </option>
                        @endforeach
                    </select>
                </div>
                
                <!-- Category Filter -->
                <div>
                    <label class="block text-sm font-bold text-slate-900 tracking-wide uppercase mb-2">
                        CATEGORY
                    </label>
                    <select name="category" class="form-select">
                        <option value="">All Categories</option>
                        @foreach($categories as $category)
                            <option value="{{ $category }}" {{ request('category') == $category ? 'selected' : '' }}>
                                {{ $category }}
                            </option>
                        @endforeach
                    </select>
                </div>
                
                <!-- Job Type Filter -->
                <div>
                    <label class="block text-sm font-bold text-slate-900 tracking-wide uppercase mb-2">
                        JOB TYPE
                    </label>
                    <select name="job_type" class="form-select">
                        <option value="">All Types</option>
                        @foreach($jobTypes as $type)
                            <option value="{{ $type }}" {{ request('job_type') == $type ? 'selected' : '' }}>
                                {{ $type }}
                            </option>
                        @endforeach
                    </select>
                </div>
                
                <!-- Search Button -->
                <div class="flex items-end">
                    <button type="submit" class="btn-primary w-full">
                        <i class="fas fa-search mr-2"></i>FILTER
                    </button>
                </div>
            </div>
            
            <!-- Additional Filters Row -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mt-4 pt-4 border-t border-gray-200">
                <!-- Experience Level -->
                <div>
                    <label class="block text-sm font-bold text-slate-900 tracking-wide uppercase mb-2">
                        EXPERIENCE
                    </label>
                    <select name="experience_level" class="form-select">
                        <option value="">All Levels</option>
                        @foreach($experienceLevels as $level)
                            <option value="{{ $level }}" {{ request('experience_level') == $level ? 'selected' : '' }}>
                                {{ $level }}
                            </option>
                        @endforeach
                    </select>
                </div>
                
                <!-- Visa Sponsorship -->
                <div>
                    <label class="block text-sm font-bold text-slate-900 tracking-wide uppercase mb-2">
                        VISA SPONSORSHIP
                    </label>
                    <select name="visa_sponsorship" class="form-select">
                        <option value="">All Jobs</option>
                        <option value="1" {{ request('visa_sponsorship') == '1' ? 'selected' : '' }}>
                            With Visa Sponsorship
                        </option>
                    </select>
                </div>
                
                <!-- Clear Filters -->
                <div class="flex items-end">
                    <a href="{{ route('jobs.jobs') }}" class="btn-outline w-full text-center">
                        <i class="fas fa-times mr-2"></i>CLEAR
                    </a>
                </div>
            </div>
        </form>
    </div>
</section>

<!-- Results Section -->
<section class="py-12 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Results Header -->
        <div class="flex flex-col md:flex-row justify-between items-center mb-8">
            <div>
                <h2 class="text-2xl font-black text-slate-900 tracking-wider uppercase">
                    JOB OPPORTUNITIES
                </h2>
                <p class="text-gray-600 mt-2">
                    Showing {{ $jobs->firstItem() ?? 0 }} - {{ $jobs->lastItem() ?? 0 }} of {{ $jobs->total() }} jobs
                </p>
            </div>
            
            <!-- Sort Options -->
            <div class="mt-4 md:mt-0">
                <select onchange="window.location.href=this.value" class="form-select">
                    <option value="{{ request()->fullUrlWithQuery(['sort' => 'latest']) }}">Latest Jobs</option>
                    <option value="{{ request()->fullUrlWithQuery(['sort' => 'featured']) }}">Featured First</option>
                    <option value="{{ request()->fullUrlWithQuery(['sort' => 'company']) }}">Company A-Z</option>
                </select>
            </div>
        </div>
        
        @if($jobs->count() > 0)
            <!-- Jobs Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
                @foreach($jobs as $job)
                    <div class="job-card group">
                        <!-- Header -->
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex items-center">
                                <!-- Company Logo Placeholder -->
                                <div class="w-16 h-16 bg-slate-900 flex items-center justify-center mr-4 flex-shrink-0">
                                    <i class="fas fa-building text-orange-500 text-xl"></i>
                                </div>
                                <div>
                                    <h3 class="text-xl font-black text-slate-900 tracking-wide uppercase group-hover:text-orange-500 transition-colors">
                                        {{ $job->title }}
                                    </h3>
                                    <p class="text-gray-600 font-semibold">{{ $job->company }}</p>
                                </div>
                            </div>
                            
                            <!-- Featured Badge -->
                            @if($job->is_featured)
                                <span class="badge-orange">FEATURED</span>
                            @endif
                        </div>
                        
                        <!-- Location & Details -->
                        <div class="mb-4">
                            <p class="text-gray-500 mb-2 flex items-center">
                                <i class="fas fa-map-marker-alt text-orange-500 mr-2"></i>
                                {{ $job->location }}, {{ $job->country }}
                            </p>
                            <div class="flex flex-wrap gap-2">
                                <span class="badge-slate">{{ $job->job_type }}</span>
                                <span class="badge-outline">{{ $job->category }}</span>
                                <span class="badge-slate">{{ $job->experience_level }}</span>
                            </div>
                        </div>
                        
                        <!-- Job Description Preview -->
                        <p class="text-gray-600 mb-4 line-clamp-2">
                            {{ Str::limit($job->description, 120) }}
                        </p>
                        
                        <!-- Benefits Icons -->
                        <div class="flex items-center gap-4 mb-4 text-sm">
                            @if($job->visa_sponsorship)
                                <span class="text-green-600 flex items-center">
                                    <i class="fas fa-passport mr-1"></i>Visa
                                </span>
                            @endif
                            @if($job->work_permit_assistance)
                                <span class="text-green-600 flex items-center">
                                    <i class="fas fa-file-contract mr-1"></i>Work Permit
                                </span>
                            @endif
                            @if($job->relocation_assistance)
                                <span class="text-green-600 flex items-center">
                                    <i class="fas fa-plane mr-1"></i>Relocation
                                </span>
                            @endif
                        </div>
                        
                        <!-- Footer -->
                        <div class="flex items-center justify-between pt-4 border-t border-gray-200">
                            <div>
                                @if($job->salary_range)
                                    <p class="text-lg font-bold text-slate-900">
                                        {{ $job->formatted_salary }}
                                    </p>
                                @endif
                                <p class="text-sm text-gray-500">
                                    {{ $job->views }} views • {{ $job->applications_count }} applications
                                </p>
                            </div>
                            <a href="{{ route('jobs.show', $job->id) }}" class="btn-primary">
                                VIEW DETAILS
                            </a>
                        </div>
                    </div>
                @endforeach
            </div>
            
            <!-- Pagination -->
            <div class="flex justify-center">
                {{ $jobs->links() }}
            </div>
        @else
            <!-- No Results -->
            <div class="text-center py-16">
                <i class="fas fa-search text-6xl text-gray-300 mb-6"></i>
                <h3 class="text-2xl font-black text-slate-900 tracking-wider uppercase mb-4">
                    NO JOBS FOUND
                </h3>
                <p class="text-gray-600 mb-8 max-w-md mx-auto">
                    We couldn't find any jobs matching your criteria. Try adjusting your filters or search terms.
                </p>
                <a href="{{ route('jobs.jobs') }}" class="btn-primary">
                    <i class="fas fa-refresh mr-2"></i>VIEW ALL JOBS
                </a>
            </div>
        @endif
    </div>
</section>

<!-- CTA Section -->
<section class="py-16 bg-slate-900 border-t-8 border-orange-500">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl font-black text-white tracking-wider uppercase mb-4">
            CAN'T FIND THE RIGHT OPPORTUNITY?
        </h2>
        <p class="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
            Let our career consultants help you find the perfect international opportunity tailored to your skills and preferences.
        </p>
        <a href="{{ route('jobs.contact') }}" class="btn-secondary">
            <i class="fas fa-comments mr-2"></i>GET PERSONALIZED ASSISTANCE
        </a>
    </div>
</section>
@endsection

@push('scripts')
<script>
    // Auto-submit form when filters change
    document.querySelectorAll('select[name="country"], select[name="category"], select[name="job_type"], select[name="experience_level"], select[name="visa_sponsorship"]').forEach(function(select) {
        select.addEventListener('change', function() {
            this.form.submit();
        });
    });
</script>
@endpush
