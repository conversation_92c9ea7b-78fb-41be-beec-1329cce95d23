@extends('layouts.app')

@section('title', 'Contact Us - Global Jobs Portal')
@section('description', 'Get in touch with our international career experts for personalized consultation and assistance with your global job search.')

@section('content')
<!-- Page Header -->
<section class="hero-section hero-pattern py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <div class="inline-flex items-center mb-8">
                <span class="badge-orange">
                    <i class="fas fa-headset mr-2"></i>GET IN TOUCH
                </span>
            </div>
            
            <h1 class="text-4xl md:text-6xl font-black text-white tracking-wider uppercase mb-6">
                CONTACT OUR
                <span class="text-orange-500 block">EXPERTS</span>
            </h1>
            
            <p class="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
                Ready to take the next step in your international career? Our team of experts is here to guide you 
                through every aspect of your global job search and relocation journey.
            </p>
        </div>
    </div>
</section>

<!-- Contact Information -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            <!-- Phone -->
            <div class="feature-card text-center">
                <div class="feature-icon mb-6">
                    <i class="fas fa-phone text-4xl text-slate-900"></i>
                </div>
                <h3 class="text-xl font-black tracking-wide uppercase mb-4">CALL US</h3>
                <p class="text-gray-600 mb-4">Speak directly with our career consultants</p>
                <div class="space-y-2">
                    <p class="text-lg font-bold text-slate-900">+****************</p>
                    <p class="text-sm text-gray-600">Mon - Fri: 9:00 AM - 6:00 PM EST</p>
                    <p class="text-sm text-gray-600">Emergency: 24/7 Support</p>
                </div>
            </div>
            
            <!-- Email -->
            <div class="feature-card text-center">
                <div class="feature-icon mb-6">
                    <i class="fas fa-envelope text-4xl text-slate-900"></i>
                </div>
                <h3 class="text-xl font-black tracking-wide uppercase mb-4">EMAIL US</h3>
                <p class="text-gray-600 mb-4">Send us your questions and requirements</p>
                <div class="space-y-2">
                    <p class="text-lg font-bold text-slate-900"><EMAIL></p>
                    <p class="text-sm text-gray-600">General Inquiries</p>
                    <p class="text-lg font-bold text-slate-900"><EMAIL></p>
                    <p class="text-sm text-gray-600">Career Consultation</p>
                </div>
            </div>
            
            <!-- Office -->
            <div class="feature-card text-center">
                <div class="feature-icon mb-6">
                    <i class="fas fa-map-marker-alt text-4xl text-slate-900"></i>
                </div>
                <h3 class="text-xl font-black tracking-wide uppercase mb-4">VISIT US</h3>
                <p class="text-gray-600 mb-4">Schedule an in-person consultation</p>
                <div class="space-y-2">
                    <p class="text-sm font-bold text-slate-900">123 Global Business Center</p>
                    <p class="text-sm text-gray-600">International District</p>
                    <p class="text-sm text-gray-600">New York, NY 10001</p>
                    <p class="text-sm text-gray-600">United States</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Contact Form -->
<section class="py-16 bg-gray-50">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        @if(session('success'))
            <div class="bg-green-100 border-l-8 border-green-500 p-6 mb-8 shadow-lg">
                <div class="flex items-center">
                    <i class="fas fa-check-circle text-green-500 text-2xl mr-4"></i>
                    <div>
                        <h3 class="text-lg font-bold text-green-800">Message Sent Successfully!</h3>
                        <p class="text-green-700">{{ session('success') }}</p>
                    </div>
                </div>
            </div>
        @endif

        @if($errors->any())
            <div class="bg-red-100 border-l-8 border-red-500 p-6 mb-8 shadow-lg">
                <div class="flex items-start">
                    <i class="fas fa-exclamation-triangle text-red-500 text-2xl mr-4 mt-1"></i>
                    <div>
                        <h3 class="text-lg font-bold text-red-800 mb-2">Please correct the following errors:</h3>
                        <ul class="list-disc list-inside text-red-700">
                            @foreach($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                </div>
            </div>
        @endif

        <div class="bg-white border-l-8 border-orange-500 p-8 shadow-lg">
            <div class="text-center mb-8">
                <h2 class="text-3xl font-black text-slate-900 tracking-wider uppercase mb-4">
                    SEND US A MESSAGE
                </h2>
                <p class="text-gray-600 text-lg">
                    Fill out the form below and our experts will get back to you within 24 hours
                </p>
            </div>
            
            <form action="{{ route('jobs.submitContact') }}" method="POST">
                @csrf
                
                <!-- Contact Type -->
                <div class="mb-8">
                    <label class="block text-sm font-bold text-slate-900 tracking-wide uppercase mb-4">
                        INQUIRY TYPE *
                    </label>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <label class="flex items-center p-4 border-2 border-gray-300 hover:border-orange-500 cursor-pointer transition-colors">
                            <input type="radio" name="inquiry_type" value="career_consultation" 
                                   {{ old('inquiry_type') == 'career_consultation' ? 'checked' : '' }}
                                   class="mr-3 h-4 w-4 text-orange-500 focus:ring-orange-500" required>
                            <div>
                                <div class="font-bold text-slate-900">Career Consultation</div>
                                <div class="text-sm text-gray-600">Get personalized career advice</div>
                            </div>
                        </label>
                        
                        <label class="flex items-center p-4 border-2 border-gray-300 hover:border-orange-500 cursor-pointer transition-colors">
                            <input type="radio" name="inquiry_type" value="job_search" 
                                   {{ old('inquiry_type') == 'job_search' ? 'checked' : '' }}
                                   class="mr-3 h-4 w-4 text-orange-500 focus:ring-orange-500" required>
                            <div>
                                <div class="font-bold text-slate-900">Job Search Assistance</div>
                                <div class="text-sm text-gray-600">Help finding specific opportunities</div>
                            </div>
                        </label>
                        
                        <label class="flex items-center p-4 border-2 border-gray-300 hover:border-orange-500 cursor-pointer transition-colors">
                            <input type="radio" name="inquiry_type" value="visa_assistance" 
                                   {{ old('inquiry_type') == 'visa_assistance' ? 'checked' : '' }}
                                   class="mr-3 h-4 w-4 text-orange-500 focus:ring-orange-500" required>
                            <div>
                                <div class="font-bold text-slate-900">Visa & Immigration</div>
                                <div class="text-sm text-gray-600">Visa sponsorship questions</div>
                            </div>
                        </label>
                    </div>
                    @error('inquiry_type')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <!-- Personal Information -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <label class="block text-sm font-bold text-slate-900 tracking-wide uppercase mb-2">
                            FIRST NAME *
                        </label>
                        <input type="text" name="first_name" value="{{ old('first_name') }}" 
                               class="form-input @error('first_name') border-red-500 @enderror" required>
                        @error('first_name')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div>
                        <label class="block text-sm font-bold text-slate-900 tracking-wide uppercase mb-2">
                            LAST NAME *
                        </label>
                        <input type="text" name="last_name" value="{{ old('last_name') }}" 
                               class="form-input @error('last_name') border-red-500 @enderror" required>
                        @error('last_name')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div>
                        <label class="block text-sm font-bold text-slate-900 tracking-wide uppercase mb-2">
                            EMAIL ADDRESS *
                        </label>
                        <input type="email" name="email" value="{{ old('email') }}" 
                               class="form-input @error('email') border-red-500 @enderror" required>
                        @error('email')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div>
                        <label class="block text-sm font-bold text-slate-900 tracking-wide uppercase mb-2">
                            PHONE NUMBER
                        </label>
                        <input type="tel" name="phone" value="{{ old('phone') }}" 
                               class="form-input @error('phone') border-red-500 @enderror">
                        @error('phone')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div>
                        <label class="block text-sm font-bold text-slate-900 tracking-wide uppercase mb-2">
                            CURRENT COUNTRY *
                        </label>
                        <input type="text" name="current_country" value="{{ old('current_country') }}" 
                               class="form-input @error('current_country') border-red-500 @enderror" required>
                        @error('current_country')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div>
                        <label class="block text-sm font-bold text-slate-900 tracking-wide uppercase mb-2">
                            TARGET COUNTRY
                        </label>
                        <select name="target_country" class="form-select @error('target_country') border-red-500 @enderror">
                            <option value="">Select Target Country</option>
                            <option value="United States" {{ old('target_country') == 'United States' ? 'selected' : '' }}>United States</option>
                            <option value="Canada" {{ old('target_country') == 'Canada' ? 'selected' : '' }}>Canada</option>
                            <option value="United Kingdom" {{ old('target_country') == 'United Kingdom' ? 'selected' : '' }}>United Kingdom</option>
                            <option value="Germany" {{ old('target_country') == 'Germany' ? 'selected' : '' }}>Germany</option>
                            <option value="Australia" {{ old('target_country') == 'Australia' ? 'selected' : '' }}>Australia</option>
                            <option value="Dubai" {{ old('target_country') == 'Dubai' ? 'selected' : '' }}>Dubai</option>
                            <option value="Singapore" {{ old('target_country') == 'Singapore' ? 'selected' : '' }}>Singapore</option>
                            <option value="Netherlands" {{ old('target_country') == 'Netherlands' ? 'selected' : '' }}>Netherlands</option>
                            <option value="Switzerland" {{ old('target_country') == 'Switzerland' ? 'selected' : '' }}>Switzerland</option>
                            <option value="Other" {{ old('target_country') == 'Other' ? 'selected' : '' }}>Other</option>
                        </select>
                        @error('target_country')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
                
                <!-- Professional Information -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <label class="block text-sm font-bold text-slate-900 tracking-wide uppercase mb-2">
                            CURRENT PROFESSION
                        </label>
                        <input type="text" name="profession" value="{{ old('profession') }}" 
                               class="form-input @error('profession') border-red-500 @enderror">
                        @error('profession')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div>
                        <label class="block text-sm font-bold text-slate-900 tracking-wide uppercase mb-2">
                            YEARS OF EXPERIENCE
                        </label>
                        <select name="experience_years" class="form-select @error('experience_years') border-red-500 @enderror">
                            <option value="">Select Experience</option>
                            <option value="0-1" {{ old('experience_years') == '0-1' ? 'selected' : '' }}>0-1 years</option>
                            <option value="2-3" {{ old('experience_years') == '2-3' ? 'selected' : '' }}>2-3 years</option>
                            <option value="4-5" {{ old('experience_years') == '4-5' ? 'selected' : '' }}>4-5 years</option>
                            <option value="6-10" {{ old('experience_years') == '6-10' ? 'selected' : '' }}>6-10 years</option>
                            <option value="10+" {{ old('experience_years') == '10+' ? 'selected' : '' }}>10+ years</option>
                        </select>
                        @error('experience_years')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
                
                <!-- Message -->
                <div class="mb-6">
                    <label class="block text-sm font-bold text-slate-900 tracking-wide uppercase mb-2">
                        MESSAGE *
                    </label>
                    <textarea name="message" rows="6" placeholder="Tell us about your career goals, specific requirements, or any questions you have..." 
                              class="form-textarea @error('message') border-red-500 @enderror" required>{{ old('message') }}</textarea>
                    @error('message')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <!-- Preferred Contact Method -->
                <div class="mb-6">
                    <label class="block text-sm font-bold text-slate-900 tracking-wide uppercase mb-2">
                        PREFERRED CONTACT METHOD
                    </label>
                    <div class="flex flex-wrap gap-4">
                        <label class="flex items-center">
                            <input type="radio" name="preferred_contact" value="email" 
                                   {{ old('preferred_contact', 'email') == 'email' ? 'checked' : '' }}
                                   class="mr-2 h-4 w-4 text-orange-500 focus:ring-orange-500">
                            <span class="text-sm font-semibold text-slate-900">Email</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="preferred_contact" value="phone" 
                                   {{ old('preferred_contact') == 'phone' ? 'checked' : '' }}
                                   class="mr-2 h-4 w-4 text-orange-500 focus:ring-orange-500">
                            <span class="text-sm font-semibold text-slate-900">Phone Call</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="preferred_contact" value="video" 
                                   {{ old('preferred_contact') == 'video' ? 'checked' : '' }}
                                   class="mr-2 h-4 w-4 text-orange-500 focus:ring-orange-500">
                            <span class="text-sm font-semibold text-slate-900">Video Call</span>
                        </label>
                    </div>
                </div>
                
                <!-- Best Time to Contact -->
                <div class="mb-8">
                    <label class="block text-sm font-bold text-slate-900 tracking-wide uppercase mb-2">
                        BEST TIME TO CONTACT
                    </label>
                    <select name="best_time" class="form-select @error('best_time') border-red-500 @enderror">
                        <option value="">Select Time Preference</option>
                        <option value="Morning (9 AM - 12 PM)" {{ old('best_time') == 'Morning (9 AM - 12 PM)' ? 'selected' : '' }}>Morning (9 AM - 12 PM)</option>
                        <option value="Afternoon (12 PM - 5 PM)" {{ old('best_time') == 'Afternoon (12 PM - 5 PM)' ? 'selected' : '' }}>Afternoon (12 PM - 5 PM)</option>
                        <option value="Evening (5 PM - 8 PM)" {{ old('best_time') == 'Evening (5 PM - 8 PM)' ? 'selected' : '' }}>Evening (5 PM - 8 PM)</option>
                        <option value="Anytime" {{ old('best_time') == 'Anytime' ? 'selected' : '' }}>Anytime</option>
                    </select>
                    @error('best_time')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <!-- Submit Button -->
                <div class="text-center">
                    <button type="submit" class="btn-primary text-lg px-12 py-4">
                        <i class="fas fa-paper-plane mr-2"></i>SEND MESSAGE
                    </button>
                </div>
            </form>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-black text-slate-900 tracking-wider uppercase mb-4 border-l-8 border-orange-500 pl-6 inline-block">
                FREQUENTLY ASKED QUESTIONS
            </h2>
            <p class="text-gray-600 text-lg">Quick answers to common questions</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div class="bg-gray-50 border-l-4 border-orange-500 p-6">
                <h3 class="text-lg font-black text-slate-900 tracking-wide uppercase mb-3">
                    HOW LONG DOES THE PROCESS TAKE?
                </h3>
                <p class="text-gray-700">
                    The timeline varies by country and visa type, but typically ranges from 3-6 months from initial 
                    consultation to job placement. We provide regular updates throughout the process.
                </p>
            </div>
            
            <div class="bg-gray-50 border-l-4 border-orange-500 p-6">
                <h3 class="text-lg font-black text-slate-900 tracking-wide uppercase mb-3">
                    WHAT ARE YOUR SERVICE FEES?
                </h3>
                <p class="text-gray-700">
                    Our initial consultation is completely free. Service fees vary based on the complexity of your case 
                    and destination country. We provide transparent pricing during your consultation.
                </p>
            </div>
            
            <div class="bg-gray-50 border-l-4 border-orange-500 p-6">
                <h3 class="text-lg font-black text-slate-900 tracking-wide uppercase mb-3">
                    DO YOU GUARANTEE JOB PLACEMENT?
                </h3>
                <p class="text-gray-700">
                    While we cannot guarantee placement, we have a 95% success rate. We work closely with you to 
                    identify suitable opportunities and provide comprehensive support throughout the process.
                </p>
            </div>
            
            <div class="bg-gray-50 border-l-4 border-orange-500 p-6">
                <h3 class="text-lg font-black text-slate-900 tracking-wide uppercase mb-3">
                    WHICH COUNTRIES DO YOU COVER?
                </h3>
                <p class="text-gray-700">
                    We have partnerships in over 50 countries including USA, Canada, UK, Germany, Australia, Dubai, 
                    Singapore, and many more. Contact us to discuss your target destination.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Emergency Contact -->
<section class="py-12 bg-slate-900 border-t-8 border-orange-500">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h2 class="text-2xl font-black text-white tracking-wider uppercase mb-4">
                NEED IMMEDIATE ASSISTANCE?
            </h2>
            <p class="text-gray-300 mb-6">
                Our emergency support team is available 24/7 for urgent matters
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="tel:+***********" class="btn-primary">
                    <i class="fas fa-phone mr-2"></i>CALL NOW: +****************
                </a>
                <a href="mailto:<EMAIL>" class="btn-secondary">
                    <i class="fas fa-envelope mr-2"></i>EMERGENCY EMAIL
                </a>
            </div>
        </div>
    </div>
</section>
@endsection
