@extends('layouts.app')

@section('title', 'Global Jobs Portal - International Work Opportunities')
@section('description', 'Find international work opportunities worldwide. Visa sponsorship, work permit assistance, and career guidance for global professionals.')

@section('content')
<!-- Hero Section -->
<section class="hero-section hero-pattern py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <!-- Badge -->
            <div class="inline-flex items-center mb-8">
                <span class="badge-orange">
                    <i class="fas fa-globe mr-2"></i>INTERNATIONAL OPPORTUNITIES
                </span>
            </div>
            
            <!-- Main Heading -->
            <h1 class="text-4xl md:text-6xl font-black text-white tracking-wider uppercase mb-6">
                YOUR GATEWAY TO
                <span class="text-orange-500 block">GLOBAL CAREERS</span>
            </h1>
            
            <!-- Subheading -->
            <p class="text-xl text-gray-300 max-w-3xl mx-auto mb-12 leading-relaxed">
                Connect with international employers offering visa sponsorship, work permit assistance, and relocation support. 
                Start your global career journey today.
            </p>
            
            <!-- CTA Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center mb-16">
                <a href="{{ route('jobs.jobs') }}" class="btn-primary">
                    <i class="fas fa-search mr-2"></i>BROWSE JOBS
                </a>
                <a href="{{ route('jobs.contact') }}" class="btn-secondary">
                    <i class="fas fa-comments mr-2"></i>GET CONSULTATION
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Search Section -->
<section class="py-16 bg-gray-100">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="search-container">
            <div class="text-center mb-8">
                <h2 class="text-3xl font-black text-slate-900 tracking-wider uppercase mb-4">
                    FIND YOUR DREAM JOB
                </h2>
                <p class="text-gray-600 text-lg">Search thousands of international opportunities</p>
            </div>
            
            <form action="{{ route('jobs.jobs') }}" method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <!-- Job Title/Keywords -->
                <div>
                    <label class="block text-sm font-bold text-slate-900 tracking-wide uppercase mb-2">
                        JOB TITLE / KEYWORDS
                    </label>
                    <input type="text" name="search" placeholder="e.g. Software Engineer" 
                           class="form-input" value="{{ request('search') }}">
                </div>
                
                <!-- Country -->
                <div>
                    <label class="block text-sm font-bold text-slate-900 tracking-wide uppercase mb-2">
                        COUNTRY
                    </label>
                    <select name="country" class="form-select">
                        <option value="">All Countries</option>
                        @foreach($countries as $country)
                            <option value="{{ $country }}" {{ request('country') == $country ? 'selected' : '' }}>
                                {{ $country }}
                            </option>
                        @endforeach
                    </select>
                </div>
                
                <!-- Category -->
                <div>
                    <label class="block text-sm font-bold text-slate-900 tracking-wide uppercase mb-2">
                        CATEGORY
                    </label>
                    <select name="category" class="form-select">
                        <option value="">All Categories</option>
                        @foreach($categories as $category)
                            <option value="{{ $category }}" {{ request('category') == $category ? 'selected' : '' }}>
                                {{ $category }}
                            </option>
                        @endforeach
                    </select>
                </div>
                
                <!-- Search Button -->
                <div class="flex items-end">
                    <button type="submit" class="btn-primary w-full">
                        <i class="fas fa-search mr-2"></i>SEARCH
                    </button>
                </div>
            </form>
        </div>
    </div>
</section>

<!-- Statistics Section -->
<section class="py-16 bg-slate-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-black text-white tracking-wider uppercase mb-4">
                GLOBAL OPPORTUNITIES
            </h2>
            <p class="text-gray-300 text-lg">Connecting talent with international employers</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div class="stat-card">
                <div class="stat-number">{{ number_format($stats['total_jobs']) }}</div>
                <div class="stat-label">Active Jobs</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ $stats['countries'] }}</div>
                <div class="stat-label">Countries</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ number_format($stats['companies']) }}</div>
                <div class="stat-label">Companies</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ $stats['featured_jobs'] }}</div>
                <div class="stat-label">Featured Jobs</div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Jobs Section -->
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-black text-slate-900 tracking-wider uppercase mb-4 border-l-8 border-orange-500 pl-6 inline-block">
                FEATURED OPPORTUNITIES
            </h2>
            <p class="text-gray-600 text-lg">Hand-picked international job opportunities</p>
        </div>
        
        @if($featuredJobs->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                @foreach($featuredJobs as $job)
                    <div class="job-card group">
                        <!-- Company Logo Placeholder -->
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-slate-900 flex items-center justify-center">
                                <i class="fas fa-building text-orange-500 text-xl"></i>
                            </div>
                            <span class="badge-orange">FEATURED</span>
                        </div>
                        
                        <!-- Job Info -->
                        <h3 class="text-xl font-black text-slate-900 tracking-wide uppercase mb-2 group-hover:text-orange-500 transition-colors">
                            {{ $job->title }}
                        </h3>
                        <p class="text-gray-600 font-semibold mb-2">{{ $job->company }}</p>
                        <p class="text-gray-500 mb-4 flex items-center">
                            <i class="fas fa-map-marker-alt text-orange-500 mr-2"></i>
                            {{ $job->location }}, {{ $job->country }}
                        </p>
                        
                        <!-- Job Details -->
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="badge-slate">{{ $job->job_type }}</span>
                            <span class="badge-outline">{{ $job->category }}</span>
                        </div>
                        
                        <!-- Benefits -->
                        <div class="flex items-center gap-4 mb-6 text-sm">
                            @if($job->visa_sponsorship)
                                <span class="text-green-600 flex items-center">
                                    <i class="fas fa-check mr-1"></i>Visa
                                </span>
                            @endif
                            @if($job->work_permit_assistance)
                                <span class="text-green-600 flex items-center">
                                    <i class="fas fa-check mr-1"></i>Work Permit
                                </span>
                            @endif
                            @if($job->relocation_assistance)
                                <span class="text-green-600 flex items-center">
                                    <i class="fas fa-check mr-1"></i>Relocation
                                </span>
                            @endif
                        </div>
                        
                        <!-- Salary -->
                        @if($job->salary_range)
                            <p class="text-lg font-bold text-slate-900 mb-4">
                                {{ $job->formatted_salary }}
                            </p>
                        @endif
                        
                        <!-- Action Button -->
                        <a href="{{ route('jobs.show', $job->id) }}" class="btn-primary w-full text-center">
                            VIEW DETAILS
                        </a>
                    </div>
                @endforeach
            </div>
            
            <!-- View All Jobs Button -->
            <div class="text-center">
                <a href="{{ route('jobs.jobs') }}" class="btn-secondary">
                    <i class="fas fa-briefcase mr-2"></i>VIEW ALL JOBS
                </a>
            </div>
        @else
            <div class="text-center py-12">
                <i class="fas fa-briefcase text-6xl text-gray-300 mb-4"></i>
                <p class="text-gray-500 text-lg">No featured jobs available at the moment.</p>
            </div>
        @endif
    </div>
</section>

<!-- Services Section -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-black text-slate-900 tracking-wider uppercase mb-4 border-l-8 border-orange-500 pl-6 inline-block">
                OUR SERVICES
            </h2>
            <p class="text-gray-600 text-lg">Comprehensive support for your international career</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div class="feature-card text-center">
                <div class="feature-icon mb-6">
                    <i class="fas fa-passport text-4xl text-slate-900"></i>
                </div>
                <h3 class="text-xl font-black tracking-wide uppercase mb-4">VISA SPONSORSHIP</h3>
                <p class="text-gray-600">Complete visa sponsorship assistance for qualified candidates seeking international employment opportunities.</p>
            </div>
            
            <div class="feature-card text-center">
                <div class="feature-icon mb-6">
                    <i class="fas fa-file-contract text-4xl text-slate-900"></i>
                </div>
                <h3 class="text-xl font-black tracking-wide uppercase mb-4">WORK PERMITS</h3>
                <p class="text-gray-600">Expert guidance through work permit applications and legal documentation for international employment.</p>
            </div>
            
            <div class="feature-card text-center">
                <div class="feature-icon mb-6">
                    <i class="fas fa-plane text-4xl text-slate-900"></i>
                </div>
                <h3 class="text-xl font-black tracking-wide uppercase mb-4">RELOCATION SUPPORT</h3>
                <p class="text-gray-600">Comprehensive relocation assistance including housing, transportation, and cultural orientation services.</p>
            </div>
            
            <div class="feature-card text-center">
                <div class="feature-icon mb-6">
                    <i class="fas fa-user-tie text-4xl text-slate-900"></i>
                </div>
                <h3 class="text-xl font-black tracking-wide uppercase mb-4">CAREER GUIDANCE</h3>
                <p class="text-gray-600">Professional career counseling and guidance to help you make informed decisions about international opportunities.</p>
            </div>
            
            <div class="feature-card text-center">
                <div class="feature-icon mb-6">
                    <i class="fas fa-handshake text-4xl text-slate-900"></i>
                </div>
                <h3 class="text-xl font-black tracking-wide uppercase mb-4">EMPLOYER MATCHING</h3>
                <p class="text-gray-600">Connect with verified international employers actively seeking qualified professionals in your field.</p>
            </div>
            
            <div class="feature-card text-center">
                <div class="feature-icon mb-6">
                    <i class="fas fa-headset text-4xl text-slate-900"></i>
                </div>
                <h3 class="text-xl font-black tracking-wide uppercase mb-4">24/7 SUPPORT</h3>
                <p class="text-gray-600">Round-the-clock support throughout your international job search and relocation process.</p>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-16 bg-slate-900 border-t-8 border-orange-500">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl font-black text-white tracking-wider uppercase mb-4">
            READY TO START YOUR GLOBAL CAREER?
        </h2>
        <p class="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
            Join thousands of professionals who have successfully launched their international careers with our assistance.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="{{ route('jobs.jobs') }}" class="btn-primary">
                <i class="fas fa-search mr-2"></i>EXPLORE OPPORTUNITIES
            </a>
            <a href="{{ route('jobs.contact') }}" class="btn-secondary">
                <i class="fas fa-phone mr-2"></i>SCHEDULE CONSULTATION
            </a>
        </div>
    </div>
</section>
@endsection
