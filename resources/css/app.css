@import 'tailwindcss';

@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';
@source '../../storage/framework/views/*.php';
@source '../**/*.blade.php';
@source '../**/*.js';

@theme {
    --font-sans: 'Inter', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
        'Segoe UI Symbol', 'Noto Color Emoji';
}

/* Custom Navigation Styles */
.nav-link {
    @apply px-4 py-2 text-sm font-bold tracking-wide uppercase text-gray-300 hover:text-orange-500 hover:bg-slate-800 transition-all duration-200 rounded-none border-l-4 border-transparent hover:border-orange-500;
}

.nav-link.active {
    @apply text-orange-500 bg-slate-800 border-orange-500;
}

.mobile-nav-link {
    @apply block px-4 py-3 text-sm font-bold tracking-wide uppercase text-gray-300 hover:text-orange-500 hover:bg-slate-800 transition-all duration-200 border-l-4 border-transparent hover:border-orange-500;
}

.mobile-nav-link.active {
    @apply text-orange-500 bg-slate-800 border-orange-500;
}

/* Custom Button Styles */
.btn-primary {
    @apply bg-orange-500 hover:bg-orange-600 text-white font-bold py-3 px-6 tracking-wide uppercase transition-all duration-200 border-none rounded-none;
}

.btn-secondary {
    @apply bg-transparent border-2 border-orange-500 text-orange-500 hover:bg-orange-500 hover:text-white font-bold py-3 px-6 tracking-wide uppercase transition-all duration-200 rounded-none;
}

.btn-outline {
    @apply bg-transparent border-2 border-slate-600 text-slate-600 hover:bg-slate-600 hover:text-white font-bold py-3 px-6 tracking-wide uppercase transition-all duration-200 rounded-none;
}

/* Custom Card Styles */
.job-card {
    @apply bg-white border-l-8 border-orange-500 shadow-lg hover:shadow-xl transition-all duration-300 p-6 rounded-none;
}

.job-card:hover {
    @apply border-slate-900 bg-slate-50;
}

.feature-card {
    @apply bg-white border-l-8 border-orange-500 p-8 shadow-lg hover:shadow-xl transition-all duration-300 rounded-none;
}

.feature-card:hover {
    @apply bg-slate-900 text-white border-orange-500;
}

.feature-card:hover .feature-icon {
    @apply text-orange-500;
}

/* Custom Form Styles */
.form-input {
    @apply w-full px-4 py-3 border-2 border-gray-300 focus:border-orange-500 focus:outline-none transition-colors duration-200 rounded-none;
}

.form-textarea {
    @apply w-full px-4 py-3 border-2 border-gray-300 focus:border-orange-500 focus:outline-none transition-colors duration-200 rounded-none resize-none;
}

.form-select {
    @apply w-full px-4 py-3 border-2 border-gray-300 focus:border-orange-500 focus:outline-none transition-colors duration-200 rounded-none bg-white;
}

/* Custom Badge Styles */
.badge-orange {
    @apply bg-orange-500 text-white px-3 py-1 text-xs font-bold tracking-wide uppercase rounded-none;
}

.badge-slate {
    @apply bg-slate-600 text-white px-3 py-1 text-xs font-bold tracking-wide uppercase rounded-none;
}

.badge-outline {
    @apply border-2 border-orange-500 text-orange-500 px-3 py-1 text-xs font-bold tracking-wide uppercase rounded-none bg-transparent;
}

/* Hero Section Styles */
.hero-section {
    @apply bg-slate-900 border-b-8 border-orange-500 relative overflow-hidden;
}

.hero-pattern {
    background-image:
        linear-gradient(45deg, rgba(248, 113, 113, 0.1) 25%, transparent 25%),
        linear-gradient(-45deg, rgba(248, 113, 113, 0.1) 25%, transparent 25%),
        linear-gradient(45deg, transparent 75%, rgba(248, 113, 113, 0.1) 75%),
        linear-gradient(-45deg, transparent 75%, rgba(248, 113, 113, 0.1) 75%);
    background-size: 60px 60px;
    background-position: 0 0, 0 30px, 30px -30px, -30px 0px;
}

/* Statistics Section */
.stat-card {
    @apply bg-white border-l-8 border-orange-500 p-6 text-center shadow-lg rounded-none;
}

.stat-number {
    @apply text-4xl font-black text-slate-900 tracking-wider;
}

.stat-label {
    @apply text-sm font-bold text-gray-600 tracking-wide uppercase mt-2;
}

/* Search Section */
.search-container {
    @apply bg-white border-l-8 border-orange-500 p-8 shadow-xl rounded-none;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .hero-section {
        @apply border-b-4;
    }

    .job-card {
        @apply border-l-4;
    }

    .feature-card {
        @apply border-l-4;
    }
}
