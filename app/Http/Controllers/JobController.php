<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Job;

class JobController extends Controller
{
    /**
     * Display the homepage with featured jobs and search
     */
    public function index()
    {
        $featuredJobs = Job::active()->featured()->latest()->take(6)->get();
        $totalJobs = Job::active()->count();
        $countries = Job::active()->distinct()->pluck('country')->sort()->values();
        $categories = Job::active()->distinct()->pluck('category')->sort()->values();

        // Statistics for homepage
        $stats = [
            'total_jobs' => $totalJobs,
            'countries' => $countries->count(),
            'companies' => Job::active()->distinct()->count('company'),
            'featured_jobs' => $featuredJobs->count()
        ];

        return view('jobs.index', compact('featuredJobs', 'stats', 'countries', 'categories'));
    }

    /**
     * Display all jobs with filtering and search
     */
    public function jobs(Request $request)
    {
        $query = Job::active()->latest();

        // Search functionality
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        // Filter by country
        if ($request->filled('country')) {
            $query->byCountry($request->country);
        }

        // Filter by category
        if ($request->filled('category')) {
            $query->byCategory($request->category);
        }

        // Filter by job type
        if ($request->filled('job_type')) {
            $query->where('job_type', $request->job_type);
        }

        // Filter by experience level
        if ($request->filled('experience_level')) {
            $query->where('experience_level', $request->experience_level);
        }

        // Filter by visa sponsorship
        if ($request->filled('visa_sponsorship')) {
            $query->where('visa_sponsorship', true);
        }

        $jobs = $query->paginate(12)->withQueryString();

        // Get filter options
        $countries = Job::active()->distinct()->pluck('country')->sort()->values();
        $categories = Job::active()->distinct()->pluck('category')->sort()->values();
        $jobTypes = Job::active()->distinct()->pluck('job_type')->sort()->values();
        $experienceLevels = Job::active()->distinct()->pluck('experience_level')->sort()->values();

        return view('jobs.jobs', compact('jobs', 'countries', 'categories', 'jobTypes', 'experienceLevels'));
    }

    /**
     * Display a specific job
     */
    public function show($id)
    {
        $job = Job::active()->findOrFail($id);

        // Increment view count
        $job->incrementViews();

        // Get related jobs (same category or country)
        $relatedJobs = Job::active()
            ->where('id', '!=', $job->id)
            ->where(function($query) use ($job) {
                $query->where('category', $job->category)
                      ->orWhere('country', $job->country);
            })
            ->latest()
            ->take(4)
            ->get();

        return view('jobs.show', compact('job', 'relatedJobs'));
    }

    /**
     * Show the application form
     */
    public function apply($id)
    {
        $job = Job::active()->findOrFail($id);
        return view('jobs.apply', compact('job'));
    }

    /**
     * Handle job application submission
     */
    public function submitApplication(Request $request, $id)
    {
        $job = Job::active()->findOrFail($id);

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',
            'cover_letter' => 'required|string|min:100',
            'resume' => 'required|file|mimes:pdf,doc,docx|max:5120', // 5MB max
        ]);

        // Handle file upload
        if ($request->hasFile('resume')) {
            $resumePath = $request->file('resume')->store('resumes', 'public');
        }

        // Here you would typically save the application to a database
        // For now, we'll just increment the application count and redirect
        $job->incrementApplications();

        // Send email notification (implement as needed)
        // Mail::to($job->contact_email)->send(new JobApplicationMail($request->all(), $resumePath));

        return redirect()->route('jobs.show', $job->id)
            ->with('success', 'Your application has been submitted successfully! We will contact you soon.');
    }

    /**
     * Search jobs via AJAX
     */
    public function search(Request $request)
    {
        $query = Job::active();

        if ($request->filled('q')) {
            $query->search($request->q);
        }

        $jobs = $query->latest()->take(10)->get(['id', 'title', 'company', 'location', 'country']);

        return response()->json($jobs);
    }

    /**
     * About page
     */
    public function about()
    {
        return view('jobs.about');
    }

    /**
     * Contact page
     */
    public function contact()
    {
        return view('jobs.contact');
    }

    /**
     * Handle contact form submission
     */
    public function submitContact(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'subject' => 'required|string|max:255',
            'message' => 'required|string|min:10',
        ]);

        // Here you would typically send an email or save to database
        // Mail::to('<EMAIL>')->send(new ContactMail($request->all()));

        return redirect()->route('jobs.contact')
            ->with('success', 'Thank you for your message! We will get back to you soon.');
    }
}
