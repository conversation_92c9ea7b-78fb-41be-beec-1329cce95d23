<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Job extends Model
{
    use HasFactory;

    protected $table = 'job_listings';

    protected $fillable = [
        'title',
        'company',
        'company_logo',
        'location',
        'country',
        'category',
        'job_type',
        'experience_level',
        'salary_range',
        'currency',
        'description',
        'requirements',
        'benefits',
        'responsibilities',
        'visa_sponsorship',
        'work_permit_assistance',
        'relocation_assistance',
        'is_featured',
        'is_active',
        'application_deadline',
        'contact_email',
        'contact_phone',
        'application_url',
        'skills',
        'languages',
        'views',
        'applications_count',
    ];

    protected $casts = [
        'skills' => 'array',
        'languages' => 'array',
        'visa_sponsorship' => 'boolean',
        'work_permit_assistance' => 'boolean',
        'relocation_assistance' => 'boolean',
        'is_featured' => 'boolean',
        'is_active' => 'boolean',
        'application_deadline' => 'date',
    ];

    // Scopes for filtering
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function scopeByCountry($query, $country)
    {
        return $query->where('country', $country);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('title', 'like', "%{$search}%")
              ->orWhere('company', 'like', "%{$search}%")
              ->orWhere('description', 'like', "%{$search}%")
              ->orWhere('location', 'like', "%{$search}%");
        });
    }

    // Accessors
    public function getFormattedSalaryAttribute()
    {
        if (!$this->salary_range) {
            return 'Salary not specified';
        }
        return $this->currency . ' ' . $this->salary_range;
    }

    public function getIsExpiredAttribute()
    {
        if (!$this->application_deadline) {
            return false;
        }
        return $this->application_deadline->isPast();
    }

    // Mutators
    public function incrementViews()
    {
        $this->increment('views');
    }

    public function incrementApplications()
    {
        $this->increment('applications_count');
    }
}
