<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Job>
 */
class JobFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $countries = ['China', 'Dubai', 'Germany', 'United Kingdom', 'United States', 'Canada', 'Australia', 'Singapore', 'Japan', 'South Korea', 'Netherlands', 'Switzerland', 'France', 'Italy', 'Spain'];
        $categories = ['Engineering', 'Healthcare', 'Finance', 'Technology', 'Education', 'Construction', 'Hospitality', 'Manufacturing', 'Sales & Marketing', 'Human Resources', 'Legal', 'Consulting'];
        $jobTypes = ['Full-time', 'Part-time', 'Contract', 'Remote'];
        $experienceLevels = ['Entry', 'Mid-level', 'Senior', 'Executive'];


        $companies = [
            'Global Tech Solutions', 'International Healthcare Group', 'WorldWide Engineering Corp',
            'Global Finance Partners', 'International Education Services', 'Worldwide Construction Ltd',
            'Global Hospitality Group', 'International Manufacturing Co', 'WorldWide Marketing Agency',
            'Global HR Solutions', 'International Legal Services', 'Worldwide Consulting Group',
            'Tech Innovations Inc', 'Healthcare Plus International', 'Engineering Excellence Ltd',
            'Finance Forward Group', 'Education First International', 'Construction Masters Corp',
            'Hospitality Leaders Ltd', 'Manufacturing Pro International', 'Marketing Experts Group'
        ];

        $skills = [
            ['Python', 'JavaScript', 'React', 'Node.js'],
            ['Java', 'Spring Boot', 'Microservices', 'AWS'],
            ['C#', '.NET', 'Azure', 'SQL Server'],
            ['Project Management', 'Agile', 'Scrum', 'Leadership'],
            ['Data Analysis', 'SQL', 'Excel', 'Tableau'],
            ['Digital Marketing', 'SEO', 'Social Media', 'Analytics'],
            ['AutoCAD', 'SolidWorks', 'Project Planning', 'Quality Control'],
            ['Patient Care', 'Medical Records', 'Healthcare Compliance', 'Emergency Response'],
            ['Financial Analysis', 'Risk Management', 'Compliance', 'Reporting'],
            ['Teaching', 'Curriculum Development', 'Student Assessment', 'Educational Technology']
        ];

        $languages = [
            ['English', 'Mandarin'],
            ['English', 'Arabic'],
            ['English', 'German'],
            ['English', 'French'],
            ['English', 'Spanish'],
            ['English', 'Japanese'],
            ['English', 'Korean'],
            ['English', 'Dutch'],
            ['English'],
            ['English', 'Italian']
        ];

        $country = $this->faker->randomElement($countries);
        $category = $this->faker->randomElement($categories);
        $company = $this->faker->randomElement($companies);

        // Generate location based on country
        $locations = [
            'China' => ['Beijing', 'Shanghai', 'Shenzhen', 'Guangzhou', 'Hangzhou'],
            'Dubai' => ['Dubai', 'Abu Dhabi', 'Sharjah'],
            'Germany' => ['Berlin', 'Munich', 'Frankfurt', 'Hamburg', 'Cologne'],
            'United Kingdom' => ['London', 'Manchester', 'Birmingham', 'Edinburgh', 'Bristol'],
            'United States' => ['New York', 'San Francisco', 'Los Angeles', 'Chicago', 'Boston'],
            'Canada' => ['Toronto', 'Vancouver', 'Montreal', 'Calgary', 'Ottawa'],
            'Australia' => ['Sydney', 'Melbourne', 'Brisbane', 'Perth', 'Adelaide'],
            'Singapore' => ['Singapore'],
            'Japan' => ['Tokyo', 'Osaka', 'Kyoto', 'Yokohama', 'Nagoya'],
            'South Korea' => ['Seoul', 'Busan', 'Incheon', 'Daegu', 'Daejeon'],
            'Netherlands' => ['Amsterdam', 'Rotterdam', 'The Hague', 'Utrecht', 'Eindhoven'],
            'Switzerland' => ['Zurich', 'Geneva', 'Basel', 'Bern', 'Lausanne'],
            'France' => ['Paris', 'Lyon', 'Marseille', 'Toulouse', 'Nice'],
            'Italy' => ['Rome', 'Milan', 'Naples', 'Turin', 'Florence'],
            'Spain' => ['Madrid', 'Barcelona', 'Valencia', 'Seville', 'Bilbao']
        ];

        return [
            'title' => $this->generateJobTitle($category),
            'company' => $company,
            'company_logo' => null,
            'location' => $this->faker->randomElement($locations[$country] ?? [$country]),
            'country' => $country,
            'category' => $category,
            'job_type' => $this->faker->randomElement($jobTypes),
            'experience_level' => $this->faker->randomElement($experienceLevels),
            'salary_range' => $this->generateSalaryRange($country),
            'currency' => $this->getCurrencyForCountry($country),
            'description' => $this->generateJobDescription($category),
            'requirements' => $this->generateJobRequirements($category),
            'benefits' => $this->generateJobBenefits(),
            'responsibilities' => $this->generateJobResponsibilities($category),
            'visa_sponsorship' => $this->faker->boolean(70), // 70% chance of visa sponsorship
            'work_permit_assistance' => $this->faker->boolean(80), // 80% chance of work permit assistance
            'relocation_assistance' => $this->faker->boolean(60), // 60% chance of relocation assistance
            'is_featured' => $this->faker->boolean(20), // 20% chance of being featured
            'is_active' => $this->faker->boolean(95), // 95% chance of being active
            'application_deadline' => $this->faker->dateTimeBetween('+1 month', '+3 months'),
            'contact_email' => $this->faker->companyEmail(),
            'contact_phone' => $this->faker->phoneNumber(),
            'application_url' => $this->faker->optional(0.3)->url(),
            'skills' => $this->faker->randomElement($skills),
            'languages' => $this->faker->randomElement($languages),
            'views' => $this->faker->numberBetween(0, 1000),
            'applications_count' => $this->faker->numberBetween(0, 50),
        ];
    }

    private function generateJobTitle($category)
    {
        $titles = [
            'Engineering' => ['Senior Software Engineer', 'Mechanical Engineer', 'Civil Engineer', 'Electrical Engineer', 'Project Engineer'],
            'Healthcare' => ['Registered Nurse', 'Medical Doctor', 'Healthcare Administrator', 'Physical Therapist', 'Medical Technician'],
            'Finance' => ['Financial Analyst', 'Investment Banker', 'Accountant', 'Risk Manager', 'Financial Controller'],
            'Technology' => ['Full Stack Developer', 'Data Scientist', 'DevOps Engineer', 'Product Manager', 'UI/UX Designer'],
            'Education' => ['International Teacher', 'Academic Coordinator', 'Education Consultant', 'Curriculum Developer', 'School Administrator'],
            'Construction' => ['Construction Manager', 'Site Engineer', 'Architect', 'Safety Officer', 'Project Coordinator'],
            'Hospitality' => ['Hotel Manager', 'Chef', 'Guest Relations Manager', 'Event Coordinator', 'Restaurant Manager'],
            'Manufacturing' => ['Production Manager', 'Quality Control Engineer', 'Operations Supervisor', 'Manufacturing Engineer', 'Supply Chain Manager'],
            'Sales & Marketing' => ['Sales Manager', 'Marketing Specialist', 'Business Development Manager', 'Digital Marketing Manager', 'Account Executive'],
            'Human Resources' => ['HR Manager', 'Recruitment Specialist', 'HR Business Partner', 'Training Coordinator', 'Compensation Analyst'],
            'Legal' => ['Corporate Lawyer', 'Legal Counsel', 'Compliance Officer', 'Legal Assistant', 'Contract Manager'],
            'Consulting' => ['Management Consultant', 'Business Analyst', 'Strategy Consultant', 'IT Consultant', 'Operations Consultant']
        ];

        return $this->faker->randomElement($titles[$category] ?? ['Professional']);
    }

    private function generateSalaryRange($country)
    {
        $ranges = [
            'China' => ['50,000 - 80,000', '80,000 - 120,000', '120,000 - 200,000'],
            'Dubai' => ['60,000 - 100,000', '100,000 - 150,000', '150,000 - 250,000'],
            'Germany' => ['45,000 - 70,000', '70,000 - 100,000', '100,000 - 150,000'],
            'United Kingdom' => ['35,000 - 55,000', '55,000 - 80,000', '80,000 - 120,000'],
            'United States' => ['60,000 - 90,000', '90,000 - 130,000', '130,000 - 200,000'],
            'Canada' => ['50,000 - 75,000', '75,000 - 110,000', '110,000 - 160,000'],
            'Australia' => ['55,000 - 80,000', '80,000 - 120,000', '120,000 - 180,000'],
            'Singapore' => ['50,000 - 80,000', '80,000 - 120,000', '120,000 - 200,000'],
            'Japan' => ['4,000,000 - 6,000,000', '6,000,000 - 9,000,000', '9,000,000 - 15,000,000'],
            'South Korea' => ['30,000,000 - 50,000,000', '50,000,000 - 80,000,000', '80,000,000 - 120,000,000'],
            'Netherlands' => ['40,000 - 65,000', '65,000 - 90,000', '90,000 - 130,000'],
            'Switzerland' => ['70,000 - 100,000', '100,000 - 140,000', '140,000 - 200,000'],
            'France' => ['35,000 - 55,000', '55,000 - 80,000', '80,000 - 120,000'],
            'Italy' => ['30,000 - 50,000', '50,000 - 70,000', '70,000 - 100,000'],
            'Spain' => ['25,000 - 40,000', '40,000 - 60,000', '60,000 - 90,000']
        ];

        return $this->faker->randomElement($ranges[$country] ?? ['40,000 - 70,000']);
    }

    private function getCurrencyForCountry($country)
    {
        $currencies = [
            'China' => 'CNY',
            'Dubai' => 'AED',
            'Germany' => 'EUR',
            'United Kingdom' => 'GBP',
            'United States' => 'USD',
            'Canada' => 'CAD',
            'Australia' => 'AUD',
            'Singapore' => 'SGD',
            'Japan' => 'JPY',
            'South Korea' => 'KRW',
            'Netherlands' => 'EUR',
            'Switzerland' => 'CHF',
            'France' => 'EUR',
            'Italy' => 'EUR',
            'Spain' => 'EUR'
        ];

        return $currencies[$country] ?? 'USD';
    }

    private function generateJobDescription($category)
    {
        $descriptions = [
            'Engineering' => 'We are seeking a talented engineer to join our international team. This role offers excellent opportunities for professional growth and the chance to work on cutting-edge projects with global impact.',
            'Healthcare' => 'Join our world-class healthcare facility and make a difference in patients\' lives. We offer comprehensive training, competitive benefits, and the opportunity to work with state-of-the-art medical equipment.',
            'Finance' => 'Exciting opportunity for a finance professional to join our growing international company. Work with diverse clients and gain valuable experience in global financial markets.',
            'Technology' => 'Be part of our innovative tech team developing solutions that impact millions of users worldwide. We offer a collaborative environment, latest technologies, and excellent career progression.',
            'Education' => 'Shape the future by joining our international education program. Teach and inspire students from diverse backgrounds while experiencing a new culture and advancing your career.',
            'Construction' => 'Lead major construction projects in a dynamic international environment. Work with cutting-edge technology and contribute to landmark developments that shape city skylines.',
            'Hospitality' => 'Deliver exceptional guest experiences in our luxury international properties. Develop your hospitality skills while working in beautiful locations around the world.',
            'Manufacturing' => 'Drive operational excellence in our state-of-the-art manufacturing facilities. Implement best practices and lead teams in producing world-class products.',
            'Sales & Marketing' => 'Expand our global market presence and drive business growth. Work with international clients and develop innovative marketing strategies for diverse markets.',
            'Human Resources' => 'Shape our global workforce and culture. Implement HR best practices across international offices and support our diverse team of professionals.',
            'Legal' => 'Provide expert legal counsel in complex international business matters. Work on high-profile cases and transactions that span multiple jurisdictions.',
            'Consulting' => 'Solve complex business challenges for our international clients. Travel the world while delivering strategic solutions that drive organizational success.'
        ];

        return $descriptions[$category] ?? 'Exciting international opportunity for a motivated professional to join our growing team and advance their career in a dynamic global environment.';
    }

    private function generateJobRequirements($category)
    {
        $requirements = [
            'Engineering' => "• Bachelor's degree in Engineering or related field\n• 3+ years of relevant experience\n• Strong problem-solving and analytical skills\n• Proficiency in relevant software and tools\n• Excellent communication skills in English\n• Willingness to relocate internationally",
            'Healthcare' => "• Valid medical license or certification\n• Minimum 2 years of clinical experience\n• Strong patient care skills\n• Knowledge of international healthcare standards\n• Fluency in English\n• Cultural sensitivity and adaptability",
            'Finance' => "• Bachelor's degree in Finance, Accounting, or Economics\n• Professional certification (CPA, CFA) preferred\n• 3+ years of financial analysis experience\n• Strong analytical and quantitative skills\n• Proficiency in financial software\n• International experience preferred",
            'Technology' => "• Bachelor's degree in Computer Science or related field\n• 3+ years of software development experience\n• Proficiency in relevant programming languages\n• Experience with agile development methodologies\n• Strong problem-solving abilities\n• Excellent English communication skills",
            'Education' => "• Bachelor's degree in Education or subject area\n• Teaching certification/license\n• Minimum 2 years of teaching experience\n• Experience with international curricula preferred\n• Strong classroom management skills\n• Cultural adaptability and flexibility",
            'Construction' => "• Bachelor's degree in Civil Engineering or Construction Management\n• 5+ years of construction project experience\n• Knowledge of international building codes\n• Strong leadership and project management skills\n• Safety certification preferred\n• Willingness to work in various climates",
            'Hospitality' => "• Bachelor's degree in Hospitality Management or related field\n• 3+ years of hospitality industry experience\n• Excellent customer service skills\n• Multi-language abilities preferred\n• Strong leadership and team management skills\n• Flexibility to work various shifts",
            'Manufacturing' => "• Bachelor's degree in Engineering or Manufacturing\n• 3+ years of manufacturing experience\n• Knowledge of lean manufacturing principles\n• Strong analytical and problem-solving skills\n• Experience with quality management systems\n• Leadership and team management abilities",
            'Sales & Marketing' => "• Bachelor's degree in Marketing, Business, or related field\n• 3+ years of sales or marketing experience\n• Strong communication and presentation skills\n• Experience with digital marketing tools\n• Results-driven with proven track record\n• International market experience preferred",
            'Human Resources' => "• Bachelor's degree in Human Resources or related field\n• HR certification (PHR, SHRM) preferred\n• 3+ years of HR experience\n• Knowledge of international employment law\n• Strong interpersonal and communication skills\n• Experience with HRIS systems",
            'Legal' => "• Law degree from accredited institution\n• Bar admission in relevant jurisdiction\n• 5+ years of legal practice experience\n• Specialization in international law preferred\n• Strong research and writing skills\n• Multi-jurisdictional experience advantageous",
            'Consulting' => "• Master's degree in Business or related field\n• 3+ years of consulting experience\n• Strong analytical and problem-solving skills\n• Excellent presentation and communication abilities\n• Experience with international clients\n• Willingness to travel extensively"
        ];

        return $requirements[$category] ?? "• Bachelor's degree in relevant field\n• 3+ years of professional experience\n• Strong communication skills\n• International experience preferred\n• Fluency in English required\n• Adaptability and cultural sensitivity";
    }

    private function generateJobBenefits()
    {
        $benefits = [
            "• Competitive international salary package",
            "• Comprehensive health and dental insurance",
            "• Visa sponsorship and work permit assistance",
            "• Relocation allowance and support",
            "• Annual flight tickets home",
            "• Professional development opportunities",
            "• International career advancement",
            "• Multicultural work environment",
            "• Language learning support",
            "• Housing allowance or accommodation",
            "• Performance-based bonuses",
            "• Retirement savings plan"
        ];

        return implode("\n", $this->faker->randomElements($benefits, $this->faker->numberBetween(6, 10)));
    }

    private function generateJobResponsibilities($category)
    {
        $responsibilities = [
            'Engineering' => "• Design and develop innovative engineering solutions\n• Collaborate with international project teams\n• Ensure compliance with global standards and regulations\n• Mentor junior engineers and provide technical guidance\n• Participate in project planning and execution\n• Conduct technical reviews and quality assessments",
            'Healthcare' => "• Provide high-quality patient care and treatment\n• Collaborate with multidisciplinary healthcare teams\n• Maintain accurate medical records and documentation\n• Participate in continuous professional development\n• Ensure compliance with healthcare protocols\n• Contribute to quality improvement initiatives",
            'Finance' => "• Conduct financial analysis and reporting\n• Develop financial models and forecasts\n• Support strategic decision-making processes\n• Ensure compliance with financial regulations\n• Collaborate with international finance teams\n• Manage risk assessment and mitigation strategies",
            'Technology' => "• Develop and maintain software applications\n• Collaborate with cross-functional teams\n• Participate in code reviews and technical discussions\n• Implement best practices and coding standards\n• Troubleshoot and resolve technical issues\n• Contribute to system architecture and design decisions",
            'Education' => "• Plan and deliver engaging lessons to international students\n• Assess student progress and provide feedback\n• Develop curriculum materials and resources\n• Participate in school events and activities\n• Collaborate with colleagues and parents\n• Contribute to school improvement initiatives",
            'Construction' => "• Manage construction projects from inception to completion\n• Coordinate with contractors, suppliers, and stakeholders\n• Ensure compliance with safety regulations and standards\n• Monitor project timelines, budgets, and quality\n• Conduct site inspections and progress reviews\n• Resolve construction-related issues and challenges",
            'Hospitality' => "• Ensure exceptional guest experiences and satisfaction\n• Manage daily operations and staff performance\n• Implement service standards and procedures\n• Handle guest complaints and special requests\n• Coordinate with various departments\n• Contribute to revenue optimization strategies",
            'Manufacturing' => "• Oversee production processes and operations\n• Implement quality control measures and standards\n• Manage production teams and schedules\n• Optimize manufacturing efficiency and productivity\n• Ensure compliance with safety regulations\n• Collaborate on continuous improvement initiatives",
            'Sales & Marketing' => "• Develop and execute sales and marketing strategies\n• Build and maintain client relationships\n• Identify new business opportunities and markets\n• Prepare proposals and presentations\n• Analyze market trends and competitor activities\n• Achieve sales targets and revenue goals",
            'Human Resources' => "• Manage recruitment and selection processes\n• Develop and implement HR policies and procedures\n• Support employee relations and performance management\n• Coordinate training and development programs\n• Ensure compliance with employment regulations\n• Contribute to organizational culture and engagement",
            'Legal' => "• Provide legal advice and counsel on various matters\n• Draft and review contracts and legal documents\n• Represent the organization in legal proceedings\n• Ensure compliance with applicable laws and regulations\n• Conduct legal research and analysis\n• Collaborate with external legal counsel when needed",
            'Consulting' => "• Analyze client business challenges and opportunities\n• Develop strategic recommendations and solutions\n• Lead project teams and client engagements\n• Prepare and deliver presentations to senior executives\n• Conduct market research and industry analysis\n• Build long-term client relationships and trust"
        ];

        return $responsibilities[$category] ?? "• Execute assigned tasks and responsibilities\n• Collaborate with team members and stakeholders\n• Contribute to organizational goals and objectives\n• Maintain professional standards and ethics\n• Participate in training and development activities\n• Support continuous improvement initiatives";
    }
}
