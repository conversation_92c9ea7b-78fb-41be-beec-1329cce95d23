<?php

namespace Database\Seeders;


use Illuminate\Database\Seeder;
use App\Models\Job;

class JobSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create 100 random jobs
        Job::factory(100)->create();

        // Create some specific featured jobs for demonstration
        $featuredJobs = [
            [
                'title' => 'Senior Software Engineer',
                'company' => 'Tech Innovations Dubai',
                'location' => 'Dubai',
                'country' => 'Dubai',
                'category' => 'Technology',
                'job_type' => 'Full-time',
                'experience_level' => 'Senior',
                'salary_range' => '120,000 - 180,000',
                'currency' => 'AED',
                'description' => 'Join our cutting-edge technology team in Dubai and work on revolutionary software solutions that impact millions of users worldwide. We offer an exceptional work environment with state-of-the-art facilities and comprehensive benefits.',
                'requirements' => "• Bachelor's degree in Computer Science or related field\n• 5+ years of software development experience\n• Expertise in React, Node.js, and cloud technologies\n• Experience with microservices architecture\n• Strong problem-solving and analytical skills\n• Excellent English communication skills",
                'benefits' => "• Competitive tax-free salary\n• Comprehensive health insurance\n• Annual flight tickets home\n• Housing allowance\n• Professional development budget\n• Visa sponsorship and work permit\n• Performance bonuses\n• International career opportunities",
                'responsibilities' => "• Lead software development projects\n• Mentor junior developers\n• Architect scalable solutions\n• Collaborate with international teams\n• Implement best practices and coding standards\n• Drive technical innovation",
                'visa_sponsorship' => true,
                'work_permit_assistance' => true,
                'relocation_assistance' => true,
                'is_featured' => true,
                'is_active' => true,
                'contact_email' => '<EMAIL>',
                'skills' => ['React', 'Node.js', 'AWS', 'Docker', 'Kubernetes'],
                'languages' => ['English', 'Arabic'],
            ],
            [
                'title' => 'International English Teacher',
                'company' => 'Global Education Academy',
                'location' => 'Shanghai',
                'country' => 'China',
                'category' => 'Education',
                'job_type' => 'Full-time',
                'experience_level' => 'Mid-level',
                'salary_range' => '180,000 - 250,000',
                'currency' => 'CNY',
                'description' => 'Teach English to motivated students in one of China\'s most dynamic cities. Experience rich Chinese culture while advancing your teaching career with excellent support and professional development opportunities.',
                'requirements' => "• Bachelor's degree in Education or English\n• TEFL/TESOL certification\n• 2+ years of teaching experience\n• Native English speaker preferred\n• Cultural adaptability and patience\n• Passion for education and student development",
                'benefits' => "• Competitive salary with housing allowance\n• Comprehensive health insurance\n• Annual flight reimbursement\n• Mandarin language classes\n• Professional development opportunities\n• Work permit and visa assistance\n• Cultural immersion programs\n• International teaching community",
                'responsibilities' => "• Plan and deliver engaging English lessons\n• Assess student progress and provide feedback\n• Participate in school events and activities\n• Collaborate with Chinese teaching staff\n• Develop curriculum materials\n• Support student language development",
                'visa_sponsorship' => true,
                'work_permit_assistance' => true,
                'relocation_assistance' => true,
                'is_featured' => true,
                'is_active' => true,
                'contact_email' => '<EMAIL>',
                'skills' => ['Teaching', 'Curriculum Development', 'Student Assessment', 'Classroom Management'],
                'languages' => ['English', 'Mandarin'],
            ],
            [
                'title' => 'Mechanical Engineer',
                'company' => 'European Engineering Solutions',
                'location' => 'Munich',
                'country' => 'Germany',
                'category' => 'Engineering',
                'job_type' => 'Full-time',
                'experience_level' => 'Senior',
                'salary_range' => '75,000 - 95,000',
                'currency' => 'EUR',
                'description' => 'Join Europe\'s leading engineering firm and work on innovative projects across automotive, aerospace, and renewable energy sectors. Excellent opportunity for career growth in Germany\'s thriving engineering industry.',
                'requirements' => "• Master's degree in Mechanical Engineering\n• 4+ years of engineering experience\n• Proficiency in CAD software (SolidWorks, AutoCAD)\n• Knowledge of German engineering standards\n• Strong analytical and problem-solving skills\n• German language skills preferred",
                'benefits' => "• Competitive European salary\n• Comprehensive social benefits\n• 30 days annual vacation\n• Professional development support\n• Work permit sponsorship\n• Relocation assistance\n• Health and dental insurance\n• Pension plan contributions",
                'responsibilities' => "• Design and develop mechanical systems\n• Conduct engineering analysis and testing\n• Collaborate with multidisciplinary teams\n• Ensure compliance with European standards\n• Support project management activities\n• Mentor junior engineers",
                'visa_sponsorship' => true,
                'work_permit_assistance' => true,
                'relocation_assistance' => true,
                'is_featured' => true,
                'is_active' => true,
                'contact_email' => '<EMAIL>',
                'skills' => ['SolidWorks', 'AutoCAD', 'ANSYS', 'Project Management', 'Quality Control'],
                'languages' => ['English', 'German'],
            ],
        ];

        foreach ($featuredJobs as $jobData) {
            Job::create($jobData);
        }
    }
}
