<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('job_listings', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('company');
            $table->string('company_logo')->nullable();
            $table->string('location');
            $table->string('country');
            $table->string('category');
            $table->string('job_type')->default('Full-time'); // Full-time, Part-time, Contract, Remote
            $table->string('experience_level')->default('Mid-level'); // Entry, Mid-level, Senior, Executive
            $table->string('salary_range')->nullable();
            $table->string('currency', 3)->default('USD');
            $table->text('description');
            $table->text('requirements');
            $table->text('benefits')->nullable();
            $table->text('responsibilities')->nullable();
            $table->boolean('visa_sponsorship')->default(false);
            $table->boolean('work_permit_assistance')->default(false);
            $table->boolean('relocation_assistance')->default(false);
            $table->boolean('is_featured')->default(false);
            $table->boolean('is_active')->default(true);
            $table->date('application_deadline')->nullable();
            $table->string('contact_email');
            $table->string('contact_phone')->nullable();
            $table->string('application_url')->nullable();
            $table->json('skills')->nullable(); // Array of required skills
            $table->json('languages')->nullable(); // Array of required languages
            $table->integer('views')->default(0);
            $table->integer('applications_count')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('job_listings');
    }
};
